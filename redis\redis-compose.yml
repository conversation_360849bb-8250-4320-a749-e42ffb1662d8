version: '3'
services:
  redis: # 服务名称
    # redis镜像版本
    image: redis:6.0.20
    # 容器名称
    container_name: redis
    ports:
      # 指定宿主机端口与容器端口映射关系，宿主机：容器
      - 6379:6379
    volumes:
      # 映射数据目录，宿主机:容器
      - /data/redis/data:/data
      # 映射配置文件目录，宿主机:容器
      - /data/redis/conf/redis.conf:/etc/redis/redis.conf
    restart: always # 容器开机自启
    privileged: true # 获取宿主机root权限
    command: [ "redis-server","/etc/redis/redis.conf" ] # 指定配置文件启动redis-server进程
