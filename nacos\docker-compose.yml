version: '3'
services:
  nacos:
    # 镜像名称
    image: nacos/nacos-server:latest
    # 容器名称
    container_name: nacos
    # 重启策略
    restart: always
    # 映射端口
    ports:
      - 8848:8848
      - 9848:9848
      - 9849:9849
    # 环境配置
    environment:
      TZ: Asia/Shanghai
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: **************
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: nacos
      MYSQL_SERVICE_PASSWORD: nacos.jdy1999123
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=UTC
    # 挂载目录
    volumes:
      # 挂载日志
      - nacos_log:/home/<USER>/logs
      # 配置挂载
      - nacos_conf:/home/<USER>/conf
volumes:
  # 如果有xxx_data这个卷就会直接使用它，否则会创建一个新的卷并使用
  nacos_log: { }
  nacos_conf: { }
