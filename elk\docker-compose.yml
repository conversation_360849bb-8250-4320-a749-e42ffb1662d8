version: '3.8'

services:
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: elasticsearch
    hostname: elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - TZ=Asia/Shanghai
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - cluster.name=elk-cluster
      - node.name=elasticsearch-node
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - elasticsearch_config:/usr/share/elasticsearch/config
      - elasticsearch_plugins:/usr/share/elasticsearch/plugins
      - elasticsearch_logs:/usr/share/elasticsearch/logs
    networks:
      - elk-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  kibana:
    image: kibana:8.11.0
    container_name: kibana
    hostname: kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    depends_on:
      elasticsearch:
        condition: service_healthy
    environment:
      - TZ=Asia/Shanghai
      - I18N_LOCALE=zh-CN
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_NAME=kibana
      - SERVER_HOST=0.0.0.0
      # 如果需要公网访问，取消注释下面的配置
      # - SERVER_PUBLICBASEURL=https://kibana.yourdomain.com
    volumes:
      - kibana_data:/usr/share/kibana/data
      - kibana_config:/usr/share/kibana/config
    networks:
      - elk-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  logstash:
    image: logstash:8.11.0
    container_name: logstash
    hostname: logstash
    restart: unless-stopped
    ports:
      - "5044:5044"  # Beats input
      - "9600:9600"  # Logstash monitoring API
      - "5000:5000/tcp"  # TCP input
      - "5000:5000/udp"  # UDP input
    environment:
      - TZ=Asia/Shanghai
      - LS_JAVA_OPTS=-Xms512m -Xmx512m
      - PIPELINE_WORKERS=2
      - LOG_LEVEL=info
    volumes:
      - logstash_data:/usr/share/logstash/data
      - logstash_pipeline:/usr/share/logstash/pipeline
      - logstash_config:/usr/share/logstash/config
      - logstash_logs:/usr/share/logstash/logs
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - elk-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600/_node/stats || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

networks:
  elk-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  elasticsearch_data:
    driver: local
  elasticsearch_config:
    driver: local
  elasticsearch_plugins:
    driver: local
  elasticsearch_logs:
    driver: local
  kibana_data:
    driver: local
  kibana_config:
    driver: local
  logstash_data:
    driver: local
  logstash_pipeline:
    driver: local
  logstash_config:
    driver: local
  logstash_logs:
    driver: local