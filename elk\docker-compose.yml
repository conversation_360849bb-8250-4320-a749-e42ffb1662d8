# ELK Stack Docker Compose 配置文件
# 包含 Elasticsearch、Logstash、<PERSON><PERSON> 三个核心组件
# 适用于日志收集、分析和可视化

version: '3.8'

services:
  # ================================
  # Elasticsearch 搜索引擎服务
  # ================================
  elasticsearch:
    image: elasticsearch:8.11.0                    # 使用官方 Elasticsearch 8.11.0 镜像
    container_name: elasticsearch                  # 容器名称
    hostname: elasticsearch                        # 容器主机名
    restart: unless-stopped                        # 重启策略：除非手动停止，否则总是重启

    # 端口映射
    ports:
      - "9200:9200"                               # HTTP API 端口（主要访问端口）
      - "9300:9300"                               # 集群通信端口（节点间通信）

    # 环境变量配置
    environment:
      - discovery.type=single-node                # 单节点模式（适用于开发/测试环境）
      - TZ=Asia/Shanghai                          # 设置时区为上海时间
      - ES_JAVA_OPTS=-Xms1g -Xmx1g               # JVM 堆内存设置：最小1GB，最大1GB
      - xpack.security.enabled=false             # 禁用 X-Pack 安全功能（简化配置）
      - xpack.security.enrollment.enabled=false  # 禁用安全注册功能
      - cluster.name=elk-cluster                  # 集群名称
      - node.name=elasticsearch-node              # 节点名称
      - bootstrap.memory_lock=true                # 锁定内存，防止 swap 影响性能

    # 系统资源限制
    ulimits:
      memlock:                                    # 内存锁定限制
        soft: -1                                  # 软限制：无限制
        hard: -1                                  # 硬限制：无限制

    # 数据卷挂载
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data         # 数据目录（索引数据）
      - elasticsearch_config:/usr/share/elasticsearch/config     # 配置目录
      - elasticsearch_plugins:/usr/share/elasticsearch/plugins   # 插件目录
      - elasticsearch_logs:/usr/share/elasticsearch/logs         # 日志目录

    # 网络配置
    networks:
      - elk-network                               # 连接到自定义网络

    # 健康检查配置
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]  # 检查集群健康状态
      interval: 30s                              # 检查间隔：30秒
      timeout: 10s                               # 超时时间：10秒
      retries: 5                                 # 重试次数：5次
      start_period: 60s                          # 启动等待时间：60秒

  # ================================
  # Kibana 可视化界面服务
  # ================================
  kibana:
    image: kibana:8.11.0                          # 使用官方 Kibana 8.11.0 镜像
    container_name: kibana                        # 容器名称
    hostname: kibana                              # 容器主机名
    restart: unless-stopped                       # 重启策略：除非手动停止，否则总是重启

    # 端口映射
    ports:
      - "5601:5601"                              # Kibana Web 界面端口

    # 依赖关系
    depends_on:
      elasticsearch:                             # 依赖 Elasticsearch 服务
        condition: service_healthy               # 等待 Elasticsearch 健康检查通过后再启动

    # 环境变量配置
    environment:
      - TZ=Asia/Shanghai                         # 设置时区为上海时间
      - I18N_LOCALE=zh-CN                        # 设置界面语言为中文
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200  # Elasticsearch 连接地址
      - SERVER_NAME=kibana                       # 服务名称
      - SERVER_HOST=0.0.0.0                      # 监听所有网络接口
      # 如果需要公网访问，取消注释下面的配置
      # - SERVER_PUBLICBASEURL=https://kibana.yourdomain.com  # 公网访问地址

    # 数据卷挂载
    volumes:
      - kibana_data:/usr/share/kibana/data       # 数据目录
      - kibana_config:/usr/share/kibana/config   # 配置目录

    # 网络配置
    networks:
      - elk-network                              # 连接到自定义网络

    # 健康检查配置
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]  # 检查 API 状态
      interval: 30s                             # 检查间隔：30秒
      timeout: 10s                              # 超时时间：10秒
      retries: 5                                # 重试次数：5次
      start_period: 60s                         # 启动等待时间：60秒

  # ================================
  # Logstash 日志处理服务
  # ================================
  logstash:
    image: logstash:8.11.0                        # 使用官方 Logstash 8.11.0 镜像
    container_name: logstash                      # 容器名称
    hostname: logstash                            # 容器主机名
    restart: unless-stopped                       # 重启策略：除非手动停止，否则总是重启

    # 端口映射
    ports:
      - "5044:5044"                              # Beats 输入端口（用于 Filebeat 等 Beats 组件）
      - "9600:9600"                              # Logstash 监控 API 端口
      - "5000:5000"                              # 通用输入端口（支持TCP和UDP）

    # 环境变量配置
    environment:
      - TZ=Asia/Shanghai                         # 设置时区为上海时间
      - LS_JAVA_OPTS=-Xms512m -Xmx512m           # JVM 堆内存设置：最小512MB，最大512MB
      - PIPELINE_WORKERS=2                       # 管道工作线程数量
      - LOG_LEVEL=info                           # 日志级别

    # 数据卷挂载
    volumes:
      - logstash_data:/usr/share/logstash/data         # 数据目录
      - logstash_pipeline:/usr/share/logstash/pipeline # 管道配置目录（存放 .conf 文件）
      - logstash_config:/usr/share/logstash/config     # 主配置目录
      - logstash_logs:/usr/share/logstash/logs         # 日志目录

    # 依赖关系
    depends_on:
      elasticsearch:                             # 依赖 Elasticsearch 服务
        condition: service_healthy               # 等待 Elasticsearch 健康检查通过后再启动

    # 网络配置
    networks:
      - elk-network                              # 连接到自定义网络

    # 健康检查配置
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600/_node/stats || exit 1"]  # 检查节点状态
      interval: 30s                             # 检查间隔：30秒
      timeout: 10s                              # 超时时间：10秒
      retries: 5                                # 重试次数：5次
      start_period: 60s                         # 启动等待时间：60秒

# ================================
# 网络配置
# ================================
networks:
  elk-network:                                   # 自定义网络名称
    driver: bridge                               # 使用桥接网络驱动
    ipam:                                        # IP 地址管理配置
      config:
        - subnet: **********/16                  # 子网范围：********** - **************

# ================================
# 数据卷配置
# ================================
volumes:
  # Elasticsearch 相关数据卷
  elasticsearch_data:                            # Elasticsearch 数据存储卷
    driver: local                                # 使用本地存储驱动
  elasticsearch_config:                          # Elasticsearch 配置文件卷
    driver: local
  elasticsearch_plugins:                         # Elasticsearch 插件卷
    driver: local
  elasticsearch_logs:                            # Elasticsearch 日志卷
    driver: local

  # Kibana 相关数据卷
  kibana_data:                                   # Kibana 数据存储卷
    driver: local
  kibana_config:                                 # Kibana 配置文件卷
    driver: local

  # Logstash 相关数据卷
  logstash_data:                                 # Logstash 数据存储卷
    driver: local
  logstash_pipeline:                             # Logstash 管道配置卷（重要：存放处理规则）
    driver: local
  logstash_config:                               # Logstash 主配置文件卷
    driver: local
  logstash_logs:                                 # Logstash 日志卷
    driver: local

# ================================
# 使用说明
# ================================
# 1. 启动所有服务：docker-compose up -d
# 2. 查看服务状态：docker-compose ps
# 3. 查看日志：docker-compose logs -f [service_name]
# 4. 停止服务：docker-compose down
# 5. 完全清理（包括数据卷）：docker-compose down -v
#
# 访问地址：
# - Elasticsearch: http://localhost:9200
# - Kibana: http://localhost:5601
# - Logstash API: http://localhost:9600
# - Logstash 输入端口: localhost:5000
#
# 注意事项：
# - 首次启动可能需要几分钟时间
# - 确保系统有足够内存（建议至少 4GB）
# - 生产环境建议启用安全认证
# - 可根据需要调整 JVM 内存设置