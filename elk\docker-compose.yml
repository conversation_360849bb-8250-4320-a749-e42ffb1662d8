version: '3'
services:
  elasticsearch:
    image: elasticsearch:7.17.6
    container_name: elasticsearch
    ports:
      - 9200:9200
      - 9300:9300
    environment:
      discovery.type: single-node
      TZ: Asia/Shanghai
      ES_JAVA_OPTS: "-Xms512m -Xmx512m"
      xpack.security.enabled: false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - elasticsearch_config:/usr/share/elasticsearch/config
      - elasticsearch_plugins:/usr/share/elasticsearch/plugins
      - elasticsearch_logs:/usr/share/elasticsearch/logs
    network_mode: "host"

  kibana:
    image: kibana:7.17.6
    container_name: kibana
    ports:
      - 5601:5601
    depends_on:
      # kibana在elasticsearch启动之后再启动
      - elasticsearch
    environment:
      #设置系统语言文中文
      I18N_LOCALE: zh-CN
      # 访问域名
      # SERVER_PUBLICBASEURL: https://kibana.cloud.com
    volumes:
      - kibana_config:/usr/share/kibana/config
    network_mode: "host"

  logstash:
    image: logstash:7.17.6
    container_name: logstash
    ports:
      - 4560:4560
    volumes:
      - logstash_pipeline:/usr/share/logstash/pipeline
      - logstash_config:/usr/share/logstash/config
    depends_on:
      - elasticsearch
    network_mode: "host"

volumes:
  elasticsearch_data:
  elasticsearch_config:
  elasticsearch_plugins:
  elasticsearch_logs:
  kibana_config:
  logstash_pipeline:
  logstash_config: