version: '3'

services:
  mysql8:
    # 镜像名
    image: mysql:8.2.0
    # 容器名(以后的控制都通过这个)
    container_name: mysql8
    # 重启策略
    restart: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      # root 密码
      MYSQL_ROOT_PASSWORD: mysql@jdy1999123JDY
      # 初始化数据库(后续的初始化sql会在这个库执行)
      MYSQL_DATABASE: nacos
      # 初始化用户(不能是root 会报错, 后续需要给新用户赋予权限)
#      MYSQL_USER: nacos
      # 用户密码
#      MYSQL_PASSWORD: nacos.jdy1999123
      # 映射端口
    ports:
      - 3307:3307
    volumes:
      # 配置挂载
      - ./conf:/etc/mysql/conf.d/
      # 数据挂载
      - ./data:/var/lib/mysql/
      # 初始化目录挂载
      - ./init:/docker-entrypoint-initdb.d/
      # 日志目录
      - ./logs:/var/log/mysql/
    command:
      # 将mysql8.0默认密码策略 修改为 原先 策略 (mysql8.0对其默认策略做了更改 会导致密码无法匹配)
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
volumes:
  conf:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/mysql/mysql8.4.4/conf
  data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/mysql/mysql8.4.4/data
  init:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/mysql/mysql8.4.4/init
  logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /volume1/docker/mysql/mysql8.4.4/logs