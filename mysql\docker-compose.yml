# MySQL 8.x Docker Compose 配置文件
# 适用于开发和生产环境的MySQL数据库服务

version: '3.8'

services:
  # ================================
  # MySQL 8.x 数据库服务
  # ================================
  mysql8:
    image: mysql:8.4.0                           # 使用MySQL 8.4.0官方镜像（LTS版本）
    container_name: mysql8                       # 容器名称
    hostname: mysql8                             # 容器主机名
    restart: unless-stopped                      # 重启策略：除非手动停止，否则总是重启

    # 环境变量配置
    environment:
      - TZ=Asia/Shanghai                         # 设置时区为上海时间
      - MYSQL_ROOT_PASSWORD=mysql@jdy1999123JDY  # root用户密码（生产环境请使用更复杂密码）
      - MYSQL_DATABASE=nacos                     # 初始化创建的数据库名
      - MYSQL_USER=nacos                         # 创建的普通用户名
      - MYSQL_PASSWORD=nacos.jdy1999123          # 普通用户密码
      - MYSQL_ROOT_HOST=%                        # 允许root用户从任何主机连接

    # 端口映射
    ports:
      - "3306:3306"                              # MySQL标准端口映射（修复原配置中的端口错误）

    # 数据卷挂载
    volumes:
      - mysql_data:/var/lib/mysql                # 数据目录（使用命名卷，更安全）
      - mysql_config:/etc/mysql/conf.d           # 配置文件目录
      - mysql_init:/docker-entrypoint-initdb.d   # 初始化SQL脚本目录
      - mysql_logs:/var/log/mysql                # 日志目录
      # 如果需要使用本地目录，可以取消注释下面的配置
      # - ./conf:/etc/mysql/conf.d/              # 本地配置目录
      # - ./data:/var/lib/mysql/                 # 本地数据目录
      # - ./init:/docker-entrypoint-initdb.d/    # 本地初始化脚本目录
      # - ./logs:/var/log/mysql/                 # 本地日志目录

    # MySQL服务器配置参数
    command: [
      "mysqld",
      "--default-authentication-plugin=mysql_native_password",  # 使用传统密码验证插件（兼容性更好）
      "--character-set-server=utf8mb4",                        # 设置字符集为utf8mb4
      "--collation-server=utf8mb4_unicode_ci",                 # 设置排序规则（推荐使用unicode_ci）
      "--explicit_defaults_for_timestamp=true",                # 启用timestamp字段的显式默认值
      "--lower_case_table_names=1",                            # 表名不区分大小写（适用于Windows/Mac）
      "--max_connections=1000",                                # 最大连接数
      "--innodb_buffer_pool_size=256M",                        # InnoDB缓冲池大小
      "--innodb_log_file_size=128M",                           # InnoDB日志文件大小
      "--innodb_flush_log_at_trx_commit=2",                    # 事务提交时的日志刷新策略
      "--slow_query_log=1",                                    # 启用慢查询日志
      "--slow_query_log_file=/var/log/mysql/slow.log",         # 慢查询日志文件路径
      "--long_query_time=2"                                    # 慢查询阈值（秒）
    ]

    # 网络配置
    networks:
      - mysql-network

    # 健康检查配置
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 30s                             # 检查间隔：30秒
      timeout: 10s                              # 超时时间：10秒
      retries: 5                                # 重试次数：5次
      start_period: 60s                         # 启动等待时间：60秒
# ================================
# 网络配置
# ================================
networks:
  mysql-network:                                 # 自定义网络名称
    driver: bridge                               # 使用桥接网络驱动
    ipam:                                        # IP地址管理配置
      config:
        - subnet: **********/16                  # 子网范围

# ================================
# 数据卷配置
# ================================
volumes:
  # MySQL 相关数据卷（推荐使用命名卷，更安全可靠）
  mysql_data:                                    # MySQL数据存储卷
    driver: local                                # 使用本地存储驱动
  mysql_config:                                  # MySQL配置文件卷
    driver: local
  mysql_init:                                    # MySQL初始化脚本卷
    driver: local
  mysql_logs:                                    # MySQL日志文件卷
    driver: local

# ================================
# 使用说明
# ================================
# 1. 启动MySQL服务：docker-compose up -d
# 2. 查看服务状态：docker-compose ps
# 3. 查看日志：docker-compose logs -f mysql8
# 4. 连接数据库：mysql -h localhost -P 3306 -u root -p
# 5. 停止服务：docker-compose down
# 6. 完全清理（包括数据）：docker-compose down -v
#
# 连接信息：
# - 主机地址: localhost
# - 端口: 3306
# - root密码: mysql@jdy1999123JDY
# - 普通用户: nacos / nacos.jdy1999123
# - 初始数据库: nacos
#
# 配置文件位置：
# - 数据目录: mysql_data 卷
# - 配置目录: mysql_config 卷
# - 初始化脚本: mysql_init 卷
# - 日志目录: mysql_logs 卷
#
# 注意事项：
# - 首次启动会进行数据库初始化，需要等待几分钟
# - 生产环境建议修改默认密码
# - 可以在 mysql_init 卷中放置 .sql 文件进行数据库初始化
# - 如需使用本地目录，请取消注释 volumes 中的本地路径配置